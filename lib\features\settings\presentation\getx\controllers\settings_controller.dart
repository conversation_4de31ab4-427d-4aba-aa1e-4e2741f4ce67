import 'package:get/get.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/tables_activation_controller.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';

class SettingsController extends GetxController {
  final LoginController loginController = Get.find<LoginController>();
  final TablesActivationController tablesActivationController =
      Get.find<TablesActivationController>();
  final CashDataSource cashDataSource = Get.find<CashDataSource>();
  final RxBool addPrinter = false.obs;
  final RxBool showTaxingSettings = false.obs;
  final RxBool addTax = false.obs;
  final RxBool kitchenPageActive = true.obs;

  void toggleAddPrinter() {
    addPrinter.value = !addPrinter.value;
  }

  void toggleTaxingSettings() {
    showTaxingSettings.value = !showTaxingSettings.value;
  }

  void toggleAddTax() {
    addTax.value = !addTax.value;
    // Save to cash data source
    cashDataSource.saveAddTaxSetting(addTax.value);
  }

  void toggleKitchenPageActive() {
    kitchenPageActive.value = !kitchenPageActive.value;
  }

  @override
  void onInit() {
    super.onInit();
    // Load addTax setting from cash data source
    addTax.value = cashDataSource.getAddTaxSetting();
  }
}

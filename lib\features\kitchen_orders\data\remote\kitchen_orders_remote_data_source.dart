import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/utils/constants.dart';
import '../../domain/entities/kitchen_orders_entity.dart';
import '../models/kitchen_orders_model.dart';

abstract class KitchenOrdersRemoteDataSource {
  Future<KitchenOrdersModel> getOrders(KitchenOrdersEntity kitchenOrdersEntity);
}

class KitchenOrdersRemoteDataSourceImpl extends KitchenOrdersRemoteDataSource {
  ApiService apiService;
  KitchenOrdersRemoteDataSourceImpl(this.apiService);
  @override
  Future<KitchenOrdersModel> getOrders(
      KitchenOrdersEntity kitchenOrdersEntity) async {
    final result = await apiService.getRequest(
      '${Constants.baseUrl}sales/pos/orders/all/${kitchenOrdersEntity.tenantId}/${kitchenOrdersEntity.companyId}/${kitchenOrdersEntity.branchId}/${kitchenOrdersEntity.statusId}/${kitchenOrdersEntity.userId}',
    );
    final responseData = result.data;

    if (result.statusCode == 200 || result.statusCode == 201) {
      return KitchenOrdersModel.fromJson(result.data);
    } else {
      throw AppExceptions(
        message: responseData['message'] ?? 'getCreateNewOrder failed',
        statusCode: result.statusCode,
        data: responseData,
      );
    }
  }
}

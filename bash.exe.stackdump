Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA16F70000 ntdll.dll
7FFA15F60000 KERNEL32.DLL
7FFA14820000 KERNELBASE.dll
7FFA16D70000 USER32.dll
7FFA14470000 win32u.dll
7FFA16B60000 GDI32.dll
7FFA14240000 gdi32full.dll
7FFA144D0000 msvcp_win.dll
7FFA14060000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA153B0000 advapi32.dll
7FFA168E0000 msvcrt.dll
7FFA16CB0000 sechost.dll
7FFA144A0000 bcrypt.dll
7FFA154C0000 RPCRT4.dll
7FFA138E0000 CRYPTBASE.DLL
7FFA143F0000 bcryptPrimitives.dll
7FFA14C80000 IMM32.DLL

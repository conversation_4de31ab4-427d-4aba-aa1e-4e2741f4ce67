import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../home/<USER>/models/orders_list_model.dart';
import '../../../../home/<USER>/entitis/orders_list_entity.dart';
import '../../../../home/<USER>/use_case/orders_list_use_case.dart';

class OrdersController extends GetxController {
  final LoginController loginController = Get.find<LoginController>();

  final RxInt orderStateIndex = 0.obs;
  final RxInt orderTypeIndex = 0.obs;
  final List<String> orderStateList = [
    'all'.tr,
    'new'.tr,
    'inProgress'.tr,
    'completed'.tr,
    'pending'.tr,
    'cancel'.tr,
  ];

  // Search functionality
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final RxBool isSearchFocused = false.obs;
  final RxString searchQuery = ''.obs;
  final RxList<Datum> filteredOrders = <Datum>[].obs;
  final RxList<Datum> allOrders = <Datum>[].obs;

  void changeOrderStateIndex(int index) {
    orderStateIndex.value = index;
    orderStateIndex.value == 0;
    searchQuery.value = '';
    getOrdersList();
  }

  void changeOrderTypeIndex(int index) {
    orderTypeIndex.value = index;
    searchQuery.value = '';
    applyFilters();
  }

  OrdersListModel ordersListModel = OrdersListModel();
  // final OrderController orderController = Get.find<OrderController>();

  final OrdersListUseCase ordersListUseCase;
  // late ScrollController scrollController;
  final loading = true.obs;
  final order = 0.obs;
  final CashDataSource cashDataSource = Get.put(CashDataSource());

  OrdersController(this.ordersListUseCase);

  Future<void> getOrdersList() async {
    final result = await ordersListUseCase(
      OrdersListEntity(
        loading: loading,
        tenantId: cashDataSource.box.read('tenantId'),
        companyId: cashDataSource.box.read('companyId'),
        branchId: cashDataSource.box.read('branchId'),
        userId: cashDataSource.box.read('userId'),
        statusId: orderStateIndex.value.toString(),
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }
        failedSnaskBar(errorMessage);
      },
      (data) {
        ordersListModel = data;
        allOrders.value = data.data ?? [];
        applyFilters();
      },
    );
  }

  // Comprehensive filtering methods
  void applyFilters() {
    List<Datum> filtered = List.from(allOrders);

    // Apply order type filter first
    if (orderTypeIndex.value != 0) {
      // 0 = All, 1 = Take Away, 2 = Dine In
      filtered = filtered.where((order) {
        // Based on the UI logic: typeId 0 = Take Away, typeId 1 = Dine In
        if (orderTypeIndex.value == 1) {
          // Take Away tab selected
          return order.typeId == 0;
        } else if (orderTypeIndex.value == 2) {
          // Dine In tab selected
          return order.typeId == 1;
        }
        return true; // Should not reach here, but just in case
      }).toList();
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((order) {
        final orderNo = order.orderNo?.toLowerCase() ?? '';
        final customerName = order.customerName?.toString().toLowerCase() ?? '';
        final searchLower = searchQuery.value.toLowerCase();

        // For order numbers: check if it's an exact match or if the search query starts with the order number
        // Remove any '#' prefix from search query for order number matching
        final cleanSearchQuery = searchLower.startsWith('#')
            ? searchLower.substring(1)
            : searchLower;

        // Check if it's an exact order number match
        bool orderNumberMatch = orderNo == cleanSearchQuery;

        // If not exact match, check if order number starts with the search query (for partial typing)
        if (!orderNumberMatch && cleanSearchQuery.isNotEmpty) {
          orderNumberMatch = orderNo.startsWith(cleanSearchQuery);
        }

        // For customer names: use contains for partial matching (more flexible for names)
        bool customerNameMatch = customerName.contains(searchLower);

        return orderNumberMatch || customerNameMatch;
      }).toList();
    }

    filteredOrders.value = filtered;
  }

  void onSearchChanged(String value) {
    searchQuery.value = value;
    applyFilters();
  }

  void onSearchSubmitted(String value) {
    searchQuery.value = value;
    applyFilters();
  }

  // Helper method to get the current order data for display
  Datum? getOrderAtIndex(int index) {
    // Always use filteredOrders as it contains the result of all applied filters
    // When no filters are applied, filteredOrders will contain all orders
    if (index >= 0 && index < filteredOrders.length) {
      return filteredOrders[index];
    }
    return null;
  }

  @override
  void onInit() async {
    super.onInit();
    orderStateIndex.value = 0;
    // Initialize search functionality
    searchController.addListener(() {
      onSearchChanged(searchController.text);
    });

    searchFocusNode.addListener(() {
      isSearchFocused.value = searchFocusNode.hasFocus;
    });

    await getOrdersList();

    // Apply initial filters (this will set filteredOrders to show all orders initially)
    applyFilters();
  }

  @override
  void onClose() {
    searchController.dispose();
    searchFocusNode.dispose();
    super.onClose();
  }
}

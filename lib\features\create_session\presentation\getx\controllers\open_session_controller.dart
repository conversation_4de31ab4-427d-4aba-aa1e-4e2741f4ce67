import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/create_session/data/models/open_session_model.dart';
import 'package:point_of_sale/features/create_session/domain/intities/open_session_intity.dart';
import 'package:point_of_sale/features/create_session/domain/use_case/open_session_use_case.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/routes/app_pages.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';

class OpenSessionController extends GetxController {
  TextEditingController openCashController = TextEditingController();
  TextEditingController openNotesController = TextEditingController();
  final loading = true.obs;
  final OpenSessionUseCase openSessionUseCase;
  final CashDataSource cashDataSource = Get.put(CashDataSource());
  OpenSessionModel openSessionModel = OpenSessionModel();

  OpenSessionController(this.openSessionUseCase);

  Future<void> openSession(String sessionCode) async {
    final result = await openSessionUseCase(
      OpenSessionIntity(
        loading: loading,
        tenantId: cashDataSource.box.read('tenantId'),
        companyId: cashDataSource.box.read('companyId'),
        branchId: cashDataSource.box.read('branchId'),
        createdBy: cashDataSource.box.read('userId'),
        openCash: openCashController.text,
        openNotes: openNotesController.text,
        sessionCode: sessionCode,
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }

        failedSnaskBar(errorMessage);
      },
      (data) {
        openSessionModel = data;

        // Save session data to cache
        if (data.data != null) {
          cashDataSource.saveSessionData(
            sessionId: data.data!.id ?? 0,
            sessionCode: data.data!.sessionCode ?? '',
            openCash: data.data!.openCash ?? '0',
            openNotes: data.data!.openNotes?.toString(),
            createdAt: data.data!.createdAt,
            updatedAt: data.data!.updatedAt,
          );

          Get.log(
              'Session saved to cache - ID: ${data.data!.id}, Code: ${data.data!.sessionCode}');
        }

        // Mark that we just created a session to avoid showing continuation dialog
        final box = GetStorage();
        box.write('just_created_session', true);
        Get.log('OpenSessionController: Marked session as just created');

        Get.toNamed(Routes.home);
        successSnackBar(
          'sessionCreatedsuccessfully'.tr,
        );
      },
    );
  }

  // Check if there's an active session
  bool hasActiveSession() {
    return cashDataSource.isSessionActive();
  }

  // Get current session data
  Map<String, dynamic> getCurrentSessionData() {
    return cashDataSource.getSessionData();
  }

  // Get current session ID
  int? getCurrentSessionId() {
    return cashDataSource.getCurrentSessionId();
  }

  // Get current session code
  String? getCurrentSessionCode() {
    return cashDataSource.getCurrentSessionCode();
  }

  // Close current session (clear from cache)
  void closeSession() {
    cashDataSource.clearSessionData();
    Get.log('Session data cleared from cache');
  }
}

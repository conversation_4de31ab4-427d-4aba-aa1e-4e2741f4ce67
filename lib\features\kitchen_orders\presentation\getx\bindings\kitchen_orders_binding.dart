import 'package:get/get.dart';

import '../../../../../injection_controller.dart';
import '../../../../orders/presentation/getx/controllers/change_order_status_controller.dart';
import '../../../../orders/presentation/getx/controllers/orders_controller.dart';
import '../controllers/kitchen_orders_controller.dart';

class KitchenOrdersBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<KitchenOrdersController>(
      () => KitchenOrdersController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<OrdersController>(
      () => OrdersController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<ChangeOrderStatusController>(
      () => ChangeOrderStatusController(
        InjectionController().getIt(),
      ),
    );
  }
}

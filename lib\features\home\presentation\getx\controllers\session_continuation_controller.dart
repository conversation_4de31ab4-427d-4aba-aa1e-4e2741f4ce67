import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:point_of_sale/core/services/session_manager.dart';
import 'package:point_of_sale/core/services/cash_data_source.dart';

class SessionContinuationController extends GetxController {
  final CashDataSource _cashDataSource = Get.find<CashDataSource>();

  // Keys for storage
  static const String _keyFromCache = 'navigation_from_cache';
  static const String _keyJustCreatedSession = 'just_created_session';

  /// Mark that user is navigating from cached session (app startup with existing session)
  void markNavigationFromCache() {
    _cashDataSource.box.write(_keyFromCache, true);
    Get.log('SessionContinuationController: Marked navigation from cache');
  }

  /// Mark that a session was just created (call this after successful session creation)
  static void markSessionAsJustCreated() {
    final box = GetStorage();
    box.write(_keyJustCreatedSession, true);
    box.write(
        _keyFromCache, false); // Clear cache flag since this is new session
    Get.log('SessionContinuationController: Marked session as just created');
  }

  /// Check if we should show the continuation dialog
  bool shouldShowContinuationDialog() {
    final fromCache = _cashDataSource.box.read(_keyFromCache) ?? false;
    final justCreatedSession =
        _cashDataSource.box.read(_keyJustCreatedSession) ?? false;
    final hasActiveSession = SessionManager.hasActiveSession;

    Get.log('SessionContinuationController: fromCache = $fromCache');
    Get.log(
        'SessionContinuationController: justCreatedSession = $justCreatedSession');
    Get.log(
        'SessionContinuationController: hasActiveSession = $hasActiveSession');

    // If user just created a session, don't show dialog
    if (justCreatedSession) {
      _cashDataSource.box.write(_keyJustCreatedSession, false);
      Get.log(
          'SessionContinuationController: Not showing dialog - session just created');
      return false;
    }

    // Show dialog only if:
    // 1. There's an active session
    // 2. User came from cache (app startup with existing session)
    final shouldShow = hasActiveSession && fromCache;

    Get.log('SessionContinuationController: shouldShow = $shouldShow');

    // Clear the fromCache flag after checking to avoid showing dialog again
    if (fromCache) {
      _cashDataSource.box.write(_keyFromCache, false);
      Get.log('SessionContinuationController: Cleared fromCache flag');
    }

    return shouldShow;
  }

  /// Show the session continuation dialog
  void showSessionContinuationDialog() {
    Get.log('SessionContinuationController: Checking if should show dialog...');

    // Debug: Print all relevant values
    Get.log('SessionContinuationController: Current storage values:');
    Get.log('  - $_keyFromCache: ${_cashDataSource.box.read(_keyFromCache)}');
    Get.log(
        '  - $_keyJustCreatedSession: ${_cashDataSource.box.read(_keyJustCreatedSession)}');
    Get.log(
        '  - SessionManager.hasActiveSession: ${SessionManager.hasActiveSession}');
    Get.log(
        '  - SessionManager.currentSessionId: ${SessionManager.currentSessionId}');
    Get.log(
        '  - SessionManager.currentSessionCode: ${SessionManager.currentSessionCode}');

    if (!shouldShowContinuationDialog()) {
      Get.log('SessionContinuationController: Not showing dialog');
      return;
    }

    Get.log(
        'SessionContinuationController: Showing session continuation dialog');

    final sessionCode = SessionManager.currentSessionCode ?? 'unknown'.tr;
    final openCash = SessionManager.openCash;

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.access_time,
                  color: Get.theme.primaryColor, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'continueSession'.tr,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'activeSessionFound'.tr,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text('${'sessionCode'.tr}: ',
                          style: const TextStyle(fontWeight: FontWeight.w600)),
                      Text(sessionCode),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text('${'openCash'.tr}: ',
                          style: const TextStyle(fontWeight: FontWeight.w600)),
                      Text(openCash),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text('doYouWantToContinue'.tr),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => _handleCloseSession(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.close, size: 18),
                const SizedBox(width: 8),
                Text('closeSession'.tr),
              ],
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () => _handleContinueSession(),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.play_arrow, size: 18),
                const SizedBox(width: 8),
                Text('continueSession'.tr),
              ],
            ),
          ),
        ],
      ),
      barrierDismissible: false, // Prevent dismissing by tapping outside
    );
  }

  /// Handle continuing with the current session
  void _handleContinueSession() {
    Get.back(); // Close the dialog
    Get.log('SessionContinuationController: User chose to continue session');
    // Session continues normally, no additional action needed
  }

  /// Handle closing the current session
  void _handleCloseSession() {
    Get.back(); // Close the dialog
    Get.log('SessionContinuationController: User chose to close session');

    // Clear session data and navigate to create session
    SessionManager.closeSession();
    Get.offAllNamed('/create-session');
  }

  /// Test method to manually show the dialog (for debugging)
  void testShowDialog() {
    Get.log('SessionContinuationController: TEST - Forcing dialog to show');

    final sessionCode = SessionManager.currentSessionCode ?? 'Test Session';
    final openCash = SessionManager.openCash;

    Get.dialog(
      AlertDialog(
        title: Text('Test Dialog'),
        content: Text(
            'This is a test dialog to verify the dialog system works.\n\nSession: $sessionCode\nCash: $openCash'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}

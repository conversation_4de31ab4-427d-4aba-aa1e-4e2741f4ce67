import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:point_of_sale/core/services/session_manager.dart';
// import 'package:point_of_sale/core/services/cash_data_source.dart';

class SessionContinuationController extends GetxController {
  // final CashDataSource _cashDataSource = Get.find<CashDataSource>();
  final GetStorage _box = GetStorage();

  // Keys for storage
  static const String _keyFromCache = 'navigation_from_cache';
  static const String _keyJustCreatedSession = 'just_created_session';

  /// Mark that user is navigating from cached session (app startup with existing session)
  void markNavigationFromCache() {
    _box.write(_keyFromCache, true);
    Get.log('SessionContinuationController: Marked navigation from cache');
  }

  /// Mark that a session was just created (call this after successful session creation)
  static void markSessionAsJustCreated() {
    final box = GetStorage();
    box.write(_keyJustCreatedSession, true);
    box.write(
        _keyFromCache, false); // Clear cache flag since this is new session
    Get.log('SessionContinuationController: Marked session as just created');
  }

  /// Check if we should show the continuation dialog
  bool shouldShowContinuationDialog() {
    final fromCache = _box.read(_keyFromCache) ?? false;
    final justCreatedSession = _box.read(_keyJustCreatedSession) ?? false;
    final hasActiveSession = SessionManager.hasActiveSession;

    Get.log('SessionContinuationController: fromCache = $fromCache');
    Get.log(
        'SessionContinuationController: justCreatedSession = $justCreatedSession');
    Get.log(
        'SessionContinuationController: hasActiveSession = $hasActiveSession');

    // Clear the justCreatedSession flag after checking
    if (justCreatedSession) {
      _box.write(_keyJustCreatedSession, false);
      Get.log('SessionContinuationController: Cleared justCreatedSession flag');
    }

    // Show dialog only if:
    // 1. There's an active session
    // 2. User came from cache (app startup with existing session)
    // 3. User didn't just create a session
    final shouldShow = hasActiveSession && fromCache && !justCreatedSession;

    Get.log('SessionContinuationController: shouldShow = $shouldShow');

    // Clear the fromCache flag after checking to avoid showing dialog again
    if (fromCache) {
      _box.write(_keyFromCache, false);
    }

    return shouldShow;
  }

  /// Show the session continuation dialog
  void showSessionContinuationDialog() {
    Get.log('SessionContinuationController: Checking if should show dialog...');

    if (!shouldShowContinuationDialog()) {
      Get.log('SessionContinuationController: Not showing dialog');
      return;
    }

    Get.log(
        'SessionContinuationController: Showing session continuation dialog');

    final sessionCode = SessionManager.currentSessionCode ?? 'unknown'.tr;
    final openCash = SessionManager.openCash;

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.access_time,
                  color: Get.theme.primaryColor, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'continueSession'.tr,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'activeSessionFound'.tr,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text('${'sessionCode'.tr}: ',
                          style: const TextStyle(fontWeight: FontWeight.w600)),
                      Text(sessionCode),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text('${'openCash'.tr}: ',
                          style: const TextStyle(fontWeight: FontWeight.w600)),
                      Text(openCash),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text('doYouWantToContinue'.tr),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => _handleCloseSession(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.close, size: 18),
                const SizedBox(width: 8),
                Text('closeSession'.tr),
              ],
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () => _handleContinueSession(),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.play_arrow, size: 18),
                const SizedBox(width: 8),
                Text('continueSession'.tr),
              ],
            ),
          ),
        ],
      ),
      barrierDismissible: false, // Prevent dismissing by tapping outside
    );
  }

  /// Handle continuing with the current session
  void _handleContinueSession() {
    Get.back(); // Close the dialog
    Get.log('SessionContinuationController: User chose to continue session');
    // Session continues normally, no additional action needed
  }

  /// Handle closing the current session
  void _handleCloseSession() {
    Get.back(); // Close the dialog
    Get.log('SessionContinuationController: User chose to close session');

    // Clear session data and navigate to create session
    SessionManager.closeSession();
    Get.offAllNamed('/create-session');
  }
}

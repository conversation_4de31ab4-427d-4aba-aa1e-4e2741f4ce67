import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/models/orders_list_model.dart';
import 'package:point_of_sale/features/home/<USER>/use_case/orders_list_use_case.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../domain/entitis/orders_list_entity.dart';

class OrdersListController extends GetxController {
  OrdersListModel ordersListModel = OrdersListModel();
  // final OrderController orderController = Get.find<OrderController>();

  final OrdersListUseCase ordersListUseCase;
  late ScrollController scrollController;
  final loading = true.obs;
  final order = 0.obs;
  final CashDataSource cashDataSource = Get.put(CashDataSource());

  OrdersListController(this.ordersListUseCase);

  Future<void> getOrdersList() async {
    final result = await ordersListUseCase(
      OrdersListEntity(
        loading: loading,
        tenantId: cashDataSource.box.read('tenantId'),
        companyId: cashDataSource.box.read('companyId'),
        branchId: cashDataSource.box.read('branchId'),
        userId: cashDataSource.box.read('userId'),
        statusId: '1',
      ),
    );
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }

        failedSnaskBar(errorMessage);
      },
      (data) {
        ordersListModel = data;
      },
    );
  }

  void scrollToSelected(int index, GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.5,
      );
    }
  }

  Future<void> handleNewOrder() async {
    await getOrdersList();
    if (ordersListModel.data != null && ordersListModel.data!.isNotEmpty) {
      int lastIndex = ordersListModel.data!.length - 1;
      order.value = lastIndex;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  void _scrollToBottom() {
    try {
      _ensureScrollControllerIsValid();
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    } catch (e) {
      Get.log(
          'ScrollController error in _scrollToBottom: $e. Reinitializing...');
      _reinitializeScrollController();
    }
  }

  void _ensureScrollControllerIsValid() {
    try {
      // Check if scrollController is still valid
      if (!scrollController.hasClients) {
        _reinitializeScrollController();
      }
    } catch (e) {
      // If there's any error, reinitialize
      _reinitializeScrollController();
    }
  }

  void _reinitializeScrollController() {
    try {
      // Dispose the old controller if it exists
      if (scrollController.hasClients) {
        scrollController.dispose();
      }
    } catch (e) {
      // Ignore disposal errors
    }
    // Create a new ScrollController
    scrollController = ScrollController();
  }

  /// Public method to validate and fix ScrollController when returning to home page
  void validateScrollController() {
    _ensureScrollControllerIsValid();
  }

  @override
  void onInit() async {
    super.onInit();
    scrollController = ScrollController();
    await getOrdersList();
    if (ordersListModel.data != null && ordersListModel.data!.isNotEmpty) {
      int lastIndex = ordersListModel.data!.length - 1;
      order.value = lastIndex;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ordersListModel.data != null && ordersListModel.data!.isNotEmpty) {
        _scrollToBottom();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    // Ensure ScrollController is valid when the controller becomes ready
    _ensureScrollControllerIsValid();
  }

  @override
  void onClose() {
    try {
      if (scrollController.hasClients) {
        scrollController.dispose();
      }
    } catch (e) {
      // Ignore disposal errors
      Get.log('Error disposing ScrollController: $e');
    }
    super.onClose();
  }
}

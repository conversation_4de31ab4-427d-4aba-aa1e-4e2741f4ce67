import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:point_of_sale/core/utils/app_assets.dart';
import 'package:point_of_sale/core/utils/size_utils.dart';
import 'package:point_of_sale/features/auth/login/presentation/getx/controllers/login_controller.dart';
import 'package:point_of_sale/features/home/<USER>/views/widgets/app_bar_search.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/session_continuation_controller.dart';
import 'package:point_of_sale/core/services/session_manager.dart';

import '../../../../core/utils/app_colors.dart';
import '../../../../core/utils/app_text_style.dart';
import '../../../../core/utils/responsive.dart';
import '../../../../core/utils/size_config.dart';
import 'widgets/custom_drawer.dart';
import 'widgets/home_body.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  late final LoginController loginController;
  late final SessionContinuationController sessionContinuationController;
  final GetStorage _box = GetStorage();

  @override
  void initState() {
    super.initState();
    loginController = Get.find<LoginController>();

    // Initialize the session continuation controller
    try {
      sessionContinuationController = Get.find<SessionContinuationController>();
    } catch (e) {
      // If not found, create it
      sessionContinuationController = Get.put(SessionContinuationController());
    }

    // Show session continuation dialog after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.log('HomeView: About to check for session continuation dialog');

      // Simple logic: if there's an active session and we didn't just create it, show dialog
      final hasActiveSession = SessionManager.hasActiveSession;
      final justCreatedSession = _box.read('just_created_session') ?? false;

      Get.log('HomeView: hasActiveSession = $hasActiveSession');
      Get.log('HomeView: justCreatedSession = $justCreatedSession');

      if (hasActiveSession && !justCreatedSession) {
        Get.log('HomeView: Showing session continuation dialog');
        sessionContinuationController.showSessionContinuationDialog();
      } else {
        Get.log(
            'HomeView: Not showing dialog - either no session or just created');
      }

      // Clear the just created flag after checking
      if (justCreatedSession) {
        _box.remove('just_created_session');
        Get.log('HomeView: Cleared just_created_session flag');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    int crossAxisCount = MediaQuery.of(context).size.width ~/
        (Responsive.isMobile(context) ? 100 : 320);
    return Scaffold(
      appBar: AppBar(
        shape: const Border(
          bottom: BorderSide(
            color: AppColors.grey1,
            width: 1,
          ),
        ),
        toolbarHeight: Responsive.isMobile(context)
            ? AppSize.height(120)
            : AppSize.height(85),
        backgroundColor: AppColors.background,
        automaticallyImplyLeading: false,
        leadingWidth: AppSize.width(270),
        centerTitle: true,
        title: SizedBox(
          height: AppSize.height(36),
          width: AppSize.width(534),
          child: AppBarSearch(),
        ),
        leading: Row(
          children: [
            SizedBox(
              width: AppSize.width(50),
            ),
            Builder(builder: (context) {
              return IconButton(
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
                icon: Icon(
                  Icons.menu,
                  size: getSize(32),
                  color: AppColors.green,
                ),
              );
            }),
            SizedBox(
              width: AppSize.width(18),
            ),
            Container(
              height: AppSize.height(34),
              width: AppSize.height(34),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.darkGreen,
              ),
              child: Center(
                child: Obx(() => Text(
                      loginController.userName.value.isNotEmpty
                          ? loginController.userName.value.substring(0, 1)
                          : '',
                      style: AppTextStyle.white17600,
                    )),
              ),
            ),
            SizedBox(
              width: AppSize.width(15),
            ),
            Obx(() => Text(
                  loginController.userName.value.isNotEmpty
                      ? loginController.userName.value
                      : '',
                  style: AppTextStyle.darkgreen14600,
                )),
          ],
        ),
        actions: [
          Padding(
            padding: EdgeInsetsDirectional.only(
              end: AppSize.width(51),
            ),
            child: Image.asset(
              AppAssets.appBarLogo,
              height: AppSize.height(50),
            ),
          ),
        ],
      ),
      drawer: Drawer(
        backgroundColor: AppColors.white,
        width: AppSize.width(372),
        child: const CustomDrawer(),
      ),
      body: SafeArea(
        top: false,
        bottom: false,
        child: HomeBody(
          crossAxisCount: crossAxisCount,
        ),
      ),
    );
  }
}

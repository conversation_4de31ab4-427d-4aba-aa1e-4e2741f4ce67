import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/session_continuation_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/services/session_manager.dart';

class HomeController extends GetxController {
  late final SessionContinuationController sessionContinuationController;
  final CashDataSource cashDataSource = Get.find<CashDataSource>();

  @override
  void onInit() {
    super.onInit();

    // find login controller (required)

    // try to find existing session continuation controller, otherwise create one
    try {
      sessionContinuationController = Get.find<SessionContinuationController>();
    } catch (e) {
      sessionContinuationController = Get.put(SessionContinuationController());
    }
  }

  @override
  void onReady() {
    super.onReady();

    // Show session continuation dialog after first frame (use Get.context)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final hasActiveSession = SessionManager.hasActiveSession;
      final justCreatedSession =
          cashDataSource.box.read('just_created_session') ?? false;

      if (hasActiveSession && !justCreatedSession) {
        final ctx = Get.context;
        if (ctx != null) {
          sessionContinuationController.showSessionContinuationDialog(ctx);
        }
      }

      // Clear the just created flag after checking
      if (justCreatedSession) {
        cashDataSource.box.remove('just_created_session');
      }
    });
  }
}

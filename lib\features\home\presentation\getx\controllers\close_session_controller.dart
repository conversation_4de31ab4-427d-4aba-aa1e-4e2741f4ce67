import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/models/close_session_model.dart';
import 'package:point_of_sale/features/home/<USER>/use_case/close_session_use_case.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/close_session_info_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/get_cash_in_out_controller.dart';

import '../../../../../core/errors/exceptions.dart';
import '../../../../../core/errors/failure.dart';
import '../../../../../core/widgets/failed_snack_bar.dart';
import '../../../../../core/services/session_manager.dart';
import '../../../../create_session/presentation/getx/controllers/open_session_controller.dart';
import '../../../domain/entitis/close_session_entity.dart';

class CloseSessionController extends GetxController {
  final CloseSessionUseCase closeSessionUseCase;
  final OpenSessionController openSessionController =
      Get.find<OpenSessionController>();
  final GetCashInOutController getCashInOutController =
      Get.find<GetCashInOutController>();
  TextEditingController closeCash = TextEditingController();
  final RxString closeCashValue = ''.obs;
  TextEditingController closeNotes = TextEditingController();
  final CloseSessionInfoController closeSessionInfoController =
      Get.find<CloseSessionInfoController>();
  final loading = true.obs;
  CloseSessionModel closeSessionModel = CloseSessionModel();
  String get cardItemPrice {
    if (closeSessionInfoController.closeSessionInfoModel.data?.sales?.isEmpty ==
        true) {
      return '0.00';
    } else {
      return closeSessionInfoController.closeSessionInfoModel.data?.sales?.first
                  .paymentMethod?.paymentType
                  .toString()
                  .trim() ==
              'CARD'
          ? closeSessionInfoController
                  .closeSessionInfoModel.data?.sales?.first.totalAmount
                  .toString() ??
              '0.00'
          : closeSessionInfoController
                  .closeSessionInfoModel.data?.sales?.last.totalAmount
                  .toString() ??
              '0.00';
    }
  }

  String get cashItemPrice {
    if (closeSessionInfoController.closeSessionInfoModel.data?.sales?.isEmpty ==
        true) {
      return '0.00';
    } else {
      closeSessionInfoController.closeSessionInfoModel.data?.sales?.first
                  .paymentMethod?.paymentType
                  .toString()
                  .trim() ==
              'CASH'
          ? closeSessionInfoController
                  .closeSessionInfoModel.data?.sales?.first.totalAmount
                  .toString() ??
              '0.00'
          : closeSessionInfoController
                  .closeSessionInfoModel.data?.sales?.last.totalAmount
                  .toString() ??
              '0.00';
    }
  }

  double get difference {
    if (closeSessionInfoController.closeSessionInfoModel.data?.sales?.isEmpty ==
        true) {
      return 0.0;
    } else {
      final closeSessionInfo =
          closeSessionInfoController.closeSessionInfoModel.data;
      if (closeSessionInfo == null) {
        return 0.0;
      }
      final openSession = double.parse(closeSessionInfo.openSession ?? '0.00');
      final ordersTotal = closeSessionInfo
                  .sales?.first.paymentMethod?.paymentType
                  .toString()
                  .trim() ==
              'CASH'
          ? double.parse(
              closeSessionInfo.sales?.first.totalAmount.toString() ?? '0.00')
          : double.parse(
              closeSessionInfo.sales?.last.totalAmount.toString() ?? '0.00');
      final cashIn = double.parse(closeSessionInfo.cashIn ?? '0.00');
      final cashOut = double.parse(closeSessionInfo.cashOut ?? '0.00');
      final counted = double.tryParse(closeCashValue.value) ?? 0.0;

      return (ordersTotal + cashIn + openSession) - (counted + cashOut);
    }
  }

  CloseSessionController(this.closeSessionUseCase);
  Future<void> closeSession() async {
    // Use cached session ID if available, fallback to openSessionController
    final sessionId = SessionManager.currentSessionId ??
        openSessionController.openSessionModel.data?.id ??
        0;

    Get.log('CloseSessionController: Closing session with ID: $sessionId');

    final result = await closeSessionUseCase(CloseSessionEntity(
      loading: loading,
      sessionId: sessionId,
      closeCash: closeCash.text,
      closeNotes: closeNotes.text,
    ));
    result.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = failure.message;
        } else if (failure is AppExceptions) {
          errorMessage = failure.message;
        } else {
          errorMessage = 'somethingWentWrongPleaseTryAgainLater'.tr;
        }

        failedSnaskBar(errorMessage);
      },
      (data) {
        closeSessionModel = data;

        // Clear session data from cache when session is successfully closed
        SessionManager.closeSession();
        Get.log('CloseSessionController: Session closed and cache cleared');

        successSnackBar('sessionClosedSuccessfully'.tr);
      },
    );
  }

  @override
  void onClose() {
    closeCash.dispose();
    closeNotes.dispose();
    super.onClose();
  }
}

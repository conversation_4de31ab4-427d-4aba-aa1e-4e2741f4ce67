import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../features/lock_screen/bindings/lock_screen_binding.dart';
import '../../features/lock_screen/views/lock_screen_view.dart';
import '../../features/auth/login/presentation/getx/bindings/auth_binding.dart';
import '../../features/auth/login/presentation/views/log_in_view.dart';
import '../../features/create_session/presentation/getx/bindings/create_session_binding.dart';
import '../../features/create_session/presentation/views/create_session_view.dart';
import '../../features/home/<USER>/getx/bindings/home_binding.dart';
import '../../features/home/<USER>/views/home_view.dart';
import '../../features/kitchen_orders/presentation/getx/bindings/kitchen_orders_binding.dart';
import '../../features/kitchen_orders/presentation/views/kitchen_orders_view.dart';
import '../../features/orders/presentation/getx/bindings/orders_binding.dart';
import '../../features/orders/presentation/views/orders_view.dart';
import '../../features/payment/presentation/getx/bindings/payment_binding.dart';
import '../../features/payment/presentation/views/payment_view.dart';
import '../../features/print/presentation/getx/bindings/print_binding.dart';
import '../../features/print/presentation/views/print_view.dart';
import '../../features/settings/presentation/getx/bindings/settings_binding.dart';
import '../../features/settings/presentation/view/settings_page.dart';
import '../../features/tables/presentation/getx/bindings/tables_binding.dart';
import '../../features/tables/presentation/views/tables_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static final box = GetStorage();

  // Dynamically set the initial route
  static String get initial {
    bool isAuthenticated = box.read('isAuthenticated') ?? false;
    bool sessionActive = box.read('session_active') ?? false;

    Get.log('AppPages.initial: isAuthenticated = $isAuthenticated');
    Get.log('AppPages.initial: sessionActive = $sessionActive');

    if (!isAuthenticated) {
      Get.log('AppPages.initial: Not authenticated, going to login');
      return Routes.login;
    }

    if (sessionActive) {
      // User is coming from cache with existing session
      // Set flag for HomeView to handle
      box.write('navigation_from_cache', true);
      Get.log('AppPages: Set navigation_from_cache = true, navigating to home');
      return Routes.home;
    } else {
      // No active session, go to create session
      Get.log('AppPages: No active session, navigating to create session');
      return Routes.createSession;
    }
  }

  static final routes = [
    GetPage(
      name: _Paths.login,
      page: () => const LogInView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.home,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.createSession,
      page: () => const CreateSessionView(),
      binding: CreateSessionBinding(),
    ),
    GetPage(
      name: _Paths.paymentView,
      page: () => const PaymentView(),
      binding: PaymentBinding(),
    ),
    GetPage(
      name: _Paths.print,
      page: () => const PrintView(),
      binding: PrintBinding(),
    ),
    GetPage(
      name: _Paths.tables,
      page: () => const TablesView(),
      binding: TablesBinding(),
    ),
    GetPage(
      name: _Paths.settings,
      page: () => const SettingsPage(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.orders,
      page: () => const OrdersView(),
      binding: OrdersBinding(),
    ),
    GetPage(
      name: _Paths.kitchenOrders,
      page: () => const KitchenOrdersView(),
      binding: KitchenOrdersBinding(),
    ),
    GetPage(
      name: _Paths.lockScreen,
      page: () => const LockScreenView(),
      binding: LockScreenBinding(),
    ),
  ];
}
